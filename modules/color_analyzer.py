#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色彩风格分析模块
分析照片的色彩特征、色调分布、饱和度等色彩风格信息
"""

import os
import pandas as pd
import numpy as np
from PIL import Image
import colorsys
from collections import Counter
from sklearn.cluster import KMeans
import cv2


class ColorAnalyzer:
    """色彩风格分析器"""
    
    def __init__(self, csv_path="data/raw.csv", report_dir="data/reports"):
        """
        初始化色彩分析器
        
        Args:
            csv_path: CSV数据文件路径
            report_dir: 报告输出目录
        """
        self.csv_path = csv_path
        self.report_dir = report_dir
        self.data = None
        self.color_data = []
        
        # 确保报告目录存在
        os.makedirs(report_dir, exist_ok=True)
    
    def load_data(self):
        """加载CSV数据"""
        try:
            self.data = pd.read_csv(self.csv_path)
            print(f"成功加载 {len(self.data)} 条照片记录")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def extract_dominant_colors(self, image_path, n_colors=5):
        """
        提取图像的主要颜色
        
        Args:
            image_path: 图像文件路径
            n_colors: 提取的主要颜色数量
            
        Returns:
            list: 主要颜色的RGB值列表
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return None
            
            # 转换为RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 调整图像大小以提高处理速度
            height, width = image.shape[:2]
            if width > 300:
                scale = 300 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height))
            
            # 重塑为二维数组
            data = image.reshape((-1, 3))
            
            # 使用K-means聚类提取主要颜色
            kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
            kmeans.fit(data)
            
            # 获取聚类中心（主要颜色）
            colors = kmeans.cluster_centers_.astype(int)
            
            # 计算每个颜色的占比
            labels = kmeans.labels_
            label_counts = Counter(labels)
            total_pixels = len(labels)
            
            color_info = []
            for i, color in enumerate(colors):
                percentage = (label_counts[i] / total_pixels) * 100
                color_info.append({
                    'rgb': tuple(color),
                    'percentage': percentage
                })
            
            # 按占比排序
            color_info.sort(key=lambda x: x['percentage'], reverse=True)
            
            return color_info
            
        except Exception as e:
            print(f"提取颜色失败 {image_path}: {e}")
            return None
    
    def rgb_to_hsv(self, rgb):
        """将RGB转换为HSV"""
        r, g, b = [x/255.0 for x in rgb]
        h, s, v = colorsys.rgb_to_hsv(r, g, b)
        return (h*360, s*100, v*100)  # 转换为常用的HSV范围
    
    def get_color_temperature(self, rgb):
        """
        估算色温（简化版本）
        
        Args:
            rgb: RGB颜色值
            
        Returns:
            str: 色温类型（暖色调/冷色调/中性）
        """
        r, g, b = rgb
        
        # 简化的色温判断
        if r > b + 30:
            return "暖色调"
        elif b > r + 30:
            return "冷色调"
        else:
            return "中性色调"
    
    def analyze_single_image_color(self, image_path):
        """
        分析单张图像的色彩特征
        
        Args:
            image_path: 图像路径
            
        Returns:
            dict: 色彩分析结果
        """
        # 提取主要颜色
        dominant_colors = self.extract_dominant_colors(image_path)
        if not dominant_colors:
            return None
        
        # 分析主色调
        main_color = dominant_colors[0]['rgb']
        h, s, v = self.rgb_to_hsv(main_color)
        
        # 色调分类
        hue_category = self.categorize_hue(h)
        
        # 饱和度分类
        saturation_category = self.categorize_saturation(s)
        
        # 亮度分类
        brightness_category = self.categorize_brightness(v)
        
        # 色温分析
        color_temperature = self.get_color_temperature(main_color)
        
        # 计算平均饱和度和亮度
        avg_saturation = np.mean([self.rgb_to_hsv(color['rgb'])[1] for color in dominant_colors])
        avg_brightness = np.mean([self.rgb_to_hsv(color['rgb'])[2] for color in dominant_colors])
        
        return {
            'main_color_rgb': main_color,
            'main_color_hsv': (h, s, v),
            'hue_category': hue_category,
            'saturation_category': saturation_category,
            'brightness_category': brightness_category,
            'color_temperature': color_temperature,
            'avg_saturation': avg_saturation,
            'avg_brightness': avg_brightness,
            'dominant_colors': dominant_colors
        }
    
    def categorize_hue(self, hue):
        """色调分类"""
        if 0 <= hue < 30 or 330 <= hue <= 360:
            return "红色系"
        elif 30 <= hue < 60:
            return "橙色系"
        elif 60 <= hue < 90:
            return "黄色系"
        elif 90 <= hue < 150:
            return "绿色系"
        elif 150 <= hue < 210:
            return "青色系"
        elif 210 <= hue < 270:
            return "蓝色系"
        elif 270 <= hue < 330:
            return "紫色系"
        else:
            return "其他"
    
    def categorize_saturation(self, saturation):
        """饱和度分类"""
        if saturation < 20:
            return "低饱和度"
        elif saturation < 50:
            return "中等饱和度"
        elif saturation < 80:
            return "高饱和度"
        else:
            return "极高饱和度"
    
    def categorize_brightness(self, brightness):
        """亮度分类"""
        if brightness < 25:
            return "很暗"
        elif brightness < 50:
            return "较暗"
        elif brightness < 75:
            return "中等亮度"
        else:
            return "明亮"

    def analyze_all_images(self):
        """分析所有图像的色彩特征"""
        if self.data is None:
            print("请先加载数据")
            return False

        print("开始分析所有图像的色彩特征...")
        self.color_data = []

        for index, row in self.data.iterrows():
            image_path = row['文件路径']

            # 检查文件是否存在
            if not os.path.exists(image_path):
                print(f"文件不存在: {image_path}")
                continue

            print(f"分析图像 {index + 1}/{len(self.data)}: {os.path.basename(image_path)}")

            # 分析色彩
            color_info = self.analyze_single_image_color(image_path)
            if color_info:
                color_info['文件名'] = row['文件名']
                color_info['文件路径'] = image_path
                self.color_data.append(color_info)

        print(f"完成色彩分析，共处理 {len(self.color_data)} 张图像")
        return True

    def analyze_hue_distribution(self):
        """分析色调分布"""
        if not self.color_data:
            return None

        hue_counts = {}
        for data in self.color_data:
            hue_category = data['hue_category']
            hue_counts[hue_category] = hue_counts.get(hue_category, 0) + 1

        total_photos = len(self.color_data)
        report = []
        for hue, count in hue_counts.items():
            percentage = (count / total_photos) * 100
            report.append({
                '色调类别': hue,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 按数量排序
        report.sort(key=lambda x: x['照片数量'], reverse=True)

        # 保存报告
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "hue_distribution_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"色调分布报告已保存到: {output_path}")

        return report_df

    def analyze_saturation_distribution(self):
        """分析饱和度分布"""
        if not self.color_data:
            return None

        saturation_counts = {}
        for data in self.color_data:
            sat_category = data['saturation_category']
            saturation_counts[sat_category] = saturation_counts.get(sat_category, 0) + 1

        total_photos = len(self.color_data)
        report = []
        for sat, count in saturation_counts.items():
            percentage = (count / total_photos) * 100
            report.append({
                '饱和度类别': sat,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 按预定义顺序排序
        order = ["低饱和度", "中等饱和度", "高饱和度", "极高饱和度"]
        report.sort(key=lambda x: order.index(x['饱和度类别']) if x['饱和度类别'] in order else 999)

        # 保存报告
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "saturation_distribution_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"饱和度分布报告已保存到: {output_path}")

        return report_df

    def analyze_brightness_distribution(self):
        """分析亮度分布"""
        if not self.color_data:
            return None

        brightness_counts = {}
        for data in self.color_data:
            brightness_category = data['brightness_category']
            brightness_counts[brightness_category] = brightness_counts.get(brightness_category, 0) + 1

        total_photos = len(self.color_data)
        report = []
        for brightness, count in brightness_counts.items():
            percentage = (count / total_photos) * 100
            report.append({
                '亮度类别': brightness,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 按预定义顺序排序
        order = ["很暗", "较暗", "中等亮度", "明亮"]
        report.sort(key=lambda x: order.index(x['亮度类别']) if x['亮度类别'] in order else 999)

        # 保存报告
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "brightness_distribution_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"亮度分布报告已保存到: {output_path}")

        return report_df

    def analyze_color_temperature_distribution(self):
        """分析色温分布"""
        if not self.color_data:
            return None

        temp_counts = {}
        for data in self.color_data:
            temp_category = data['color_temperature']
            temp_counts[temp_category] = temp_counts.get(temp_category, 0) + 1

        total_photos = len(self.color_data)
        report = []
        for temp, count in temp_counts.items():
            percentage = (count / total_photos) * 100
            report.append({
                '色温类别': temp,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 按数量排序
        report.sort(key=lambda x: x['照片数量'], reverse=True)

        # 保存报告
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "color_temperature_distribution_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"色温分布报告已保存到: {output_path}")

        return report_df

    def save_detailed_color_data(self):
        """保存详细的色彩数据"""
        if not self.color_data:
            return None

        detailed_data = []
        for data in self.color_data:
            row = {
                '文件名': data['文件名'],
                '文件路径': data['文件路径'],
                '主色调RGB': str(data['main_color_rgb']),
                '主色调HSV': f"H:{data['main_color_hsv'][0]:.1f}, S:{data['main_color_hsv'][1]:.1f}, V:{data['main_color_hsv'][2]:.1f}",
                '色调类别': data['hue_category'],
                '饱和度类别': data['saturation_category'],
                '亮度类别': data['brightness_category'],
                '色温类别': data['color_temperature'],
                '平均饱和度': round(data['avg_saturation'], 2),
                '平均亮度': round(data['avg_brightness'], 2)
            }
            detailed_data.append(row)

        # 保存详细数据
        detailed_df = pd.DataFrame(detailed_data)
        output_path = os.path.join(self.report_dir, "detailed_color_analysis.csv")
        detailed_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"详细色彩分析数据已保存到: {output_path}")

        return detailed_df

    def generate_color_summary_report(self):
        """生成色彩分析汇总报告"""
        if not self.color_data:
            return False

        # 计算统计信息
        total_photos = len(self.color_data)
        avg_saturation = np.mean([data['avg_saturation'] for data in self.color_data])
        avg_brightness = np.mean([data['avg_brightness'] for data in self.color_data])

        # 找出最常见的色彩特征
        hue_counts = {}
        sat_counts = {}
        brightness_counts = {}
        temp_counts = {}

        for data in self.color_data:
            hue_counts[data['hue_category']] = hue_counts.get(data['hue_category'], 0) + 1
            sat_counts[data['saturation_category']] = sat_counts.get(data['saturation_category'], 0) + 1
            brightness_counts[data['brightness_category']] = brightness_counts.get(data['brightness_category'], 0) + 1
            temp_counts[data['color_temperature']] = temp_counts.get(data['color_temperature'], 0) + 1

        most_common_hue = max(hue_counts, key=hue_counts.get)
        most_common_saturation = max(sat_counts, key=sat_counts.get)
        most_common_brightness = max(brightness_counts, key=brightness_counts.get)
        most_common_temperature = max(temp_counts, key=temp_counts.get)

        # 创建汇总报告
        summary_lines = [
            "照片色彩风格分析汇总报告",
            "=" * 50,
            f"分析时间: {pd.Timestamp.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
            f"分析照片总数: {total_photos}",
            "",
            "整体色彩特征:",
            "-" * 30,
            f"平均饱和度: {avg_saturation:.2f}%",
            f"平均亮度: {avg_brightness:.2f}%",
            "",
            "最常见的色彩特征:",
            "-" * 30,
            f"主要色调: {most_common_hue} ({hue_counts[most_common_hue]}张, {hue_counts[most_common_hue]/total_photos*100:.1f}%)",
            f"主要饱和度: {most_common_saturation} ({sat_counts[most_common_saturation]}张, {sat_counts[most_common_saturation]/total_photos*100:.1f}%)",
            f"主要亮度: {most_common_brightness} ({brightness_counts[most_common_brightness]}张, {brightness_counts[most_common_brightness]/total_photos*100:.1f}%)",
            f"主要色温: {most_common_temperature} ({temp_counts[most_common_temperature]}张, {temp_counts[most_common_temperature]/total_photos*100:.1f}%)",
            "",
            "详细分析报告文件:",
            "-" * 30,
            "- hue_distribution_report.csv (色调分布)",
            "- saturation_distribution_report.csv (饱和度分布)",
            "- brightness_distribution_report.csv (亮度分布)",
            "- color_temperature_distribution_report.csv (色温分布)",
            "- detailed_color_analysis.csv (详细色彩数据)",
            ""
        ]

        # 保存汇总报告
        summary_path = os.path.join(self.report_dir, "color_analysis_summary.txt")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary_lines))

        print(f"色彩分析汇总报告已保存到: {summary_path}")
        return True

    def generate_all_color_reports(self):
        """生成所有色彩分析报告"""
        if not self.load_data():
            return False

        print("开始生成色彩分析报告...")
        print("=" * 50)

        # 分析所有图像
        if not self.analyze_all_images():
            return False

        if not self.color_data:
            print("没有成功分析的图像数据")
            return False

        # 生成各种分析报告
        self.analyze_hue_distribution()
        self.analyze_saturation_distribution()
        self.analyze_brightness_distribution()
        self.analyze_color_temperature_distribution()
        self.save_detailed_color_data()
        self.generate_color_summary_report()

        print("=" * 50)
        print("色彩分析报告生成完成！")
        return True
