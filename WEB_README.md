# ImageAPP Web应用 - 照片分析可视化平台

基于FastAPI构建的现代化照片分析Web应用，提供直观的数据可视化和深度洞察分析。

## 🌟 主要功能

### 📊 多维度数据分析
- **总览仪表板**: 照片总数、最爱设备、最爱镜头统计
- **参数风格分析**: 光圈、快门、ISO、焦距分布图表
- **器材洞察**: 设备和镜头使用频率饼状图
- **时间分析**: 拍摄时段、季节、月度趋势曲线图
- **色彩分析**: 色调、饱和度、亮度、色温分布

### 🎯 智能洞察系统
- 自动分析摄影习惯和风格偏好
- 生成个性化的拍摄建议和洞察
- 识别创作模式和时间规律

### 🔧 便捷管理功能
- **系统文件夹选择器**: 跨平台图形界面选择文件夹
- **可视化路径管理**: 直观的路径添加和删除
- **自动照片扫描和去重**: 智能处理重复照片
- **实时处理进度显示**: 可视化处理状态
- **响应式设计**: 完美适配桌面和移动端

## 🚀 快速开始

### 方法一：一键启动（推荐）
```bash
python run_webapp.py
```

### 方法二：手动启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python app.py
```

应用将在 `http://127.0.0.1:8000` 启动，浏览器会自动打开。

## 📋 使用流程

### 1. 初始设置
- 首次访问会进入设置页面
- 点击"选择照片文件夹"按钮打开系统文件夹选择器
- 支持添加多个路径，也可手动输入路径（备用选项）
- 跨平台支持：Windows、macOS、Linux

### 2. 开始分析
- 点击"开始分析照片"按钮
- 系统自动扫描所有路径下的照片
- 实时显示处理进度

### 3. 查看分析结果
- **总览**: 查看基本统计信息
- **参数风格**: 分析拍摄参数偏好
- **器材洞察**: 了解设备使用习惯
- **时间分析**: 发现拍摄时间规律
- **色彩分析**: 探索色彩风格倾向

## 🎨 界面特色

### 现代化设计
- 基于Bootstrap 5的响应式界面
- 渐变色彩和流畅动画效果
- 直观的图标和视觉引导

### 交互式图表
- 使用Chart.js构建的动态图表
- 支持悬停查看详细数据
- 多种图表类型：柱状图、饼状图、曲线图

### 智能洞察
- 每个分析模块都包含专业洞察
- 个性化的摄影风格分析
- 实用的拍摄建议和趋势预测

## 📁 项目结构

```
ImageAPP/
├── app.py                 # FastAPI主应用
├── run_webapp.py          # 一键启动脚本
├── requirements.txt       # 依赖包列表
├── config.json           # 配置文件（自动生成）
├── templates/            # HTML模板
│   ├── base.html         # 基础模板
│   ├── setup.html        # 设置页面
│   └── dashboard.html    # 仪表板页面
├── static/               # 静态资源
│   └── style.css         # 自定义样式
├── modules/              # 核心处理模块
└── data/                 # 数据文件
    └── raw.csv           # 照片数据
```

## 🔧 技术栈

### 后端技术
- **FastAPI**: 现代化Python Web框架
- **Uvicorn**: ASGI服务器
- **Pandas**: 数据处理和分析
- **PIL/Pillow**: 图像处理

### 前端技术
- **Bootstrap 5**: 响应式UI框架
- **Chart.js**: 数据可视化图表库
- **Font Awesome**: 图标库
- **Jinja2**: 模板引擎

### 跨平台文件夹选择器
- **macOS**: 使用AppleScript调用系统对话框
- **Windows**: 使用PowerShell和.NET Framework
- **Linux**: 支持zenity和kdialog
- **通用回退**: tkinter作为最后的备选方案

## 📊 支持的图表类型

### 柱状图 (Bar Charts)
- 光圈分布
- 快门速度分布
- ISO分布
- 焦距分布

### 饼状图 (Pie Charts)
- 设备使用分布
- 镜头使用分布
- 季节分布
- 色彩分布

### 曲线图 (Line Charts)
- 时间段趋势
- 月度拍摄趋势

## 🎯 洞察分析功能

### 摄影风格分析
- 大光圈 vs 小光圈偏好
- 高ISO vs 低ISO使用习惯
- 快门速度偏好分析

### 器材使用洞察
- 设备专一度分析
- 镜头搭配合理性
- 器材利用率统计

### 时间模式识别
- 最佳拍摄时段
- 季节性创作规律
- 月度活跃度分析

## 🔄 数据处理流程

1. **路径扫描**: 递归扫描指定目录
2. **EXIF提取**: 读取照片元数据信息
3. **数据清洗**: 标准化和验证数据
4. **去重处理**: 基于内容的重复检测
5. **统计分析**: 多维度数据统计
6. **可视化**: 生成交互式图表

## 🛠️ 自定义配置

### 路径管理
- 支持添加/删除多个照片路径
- 自动保存配置到 `config.json`
- 支持相对路径和绝对路径

### 数据存储
- 所有分析数据保存到 `data/raw.csv`
- 支持增量更新和重新分析
- 自动备份重要数据

## 🔍 故障排除

### 常见问题
1. **依赖安装失败**: 使用 `run_webapp.py` 自动安装
2. **照片无法读取**: 检查文件格式和权限
3. **图表不显示**: 确保网络连接正常（CDN资源）

### 性能优化
- 大量照片建议分批处理
- 定期清理重复数据
- 使用SSD存储提升处理速度

## 📈 未来规划

- [ ] 支持更多图像格式
- [ ] 添加地理位置分析
- [ ] 集成机器学习分析
- [ ] 支持批量导出报告
- [ ] 添加用户偏好设置

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。
