#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色彩风格分析运行脚本
分析照片的色彩特征并生成详细报告
"""

from modules.color_analyzer import ColorAnalyzer


def main():
    """主函数"""
    print("照片色彩风格分析工具")
    print("=" * 50)
    print("注意：此功能需要安装额外的依赖包：")
    print("pip install opencv-python scikit-learn")
    print("=" * 50)
    
    try:
        # 创建色彩分析器实例
        analyzer = ColorAnalyzer()
        
        # 生成所有色彩分析报告
        success = analyzer.generate_all_color_reports()
        
        if success:
            print("\n色彩分析完成！")
            print("请查看 data/reports/ 目录下的色彩分析报告文件：")
            print("- hue_distribution_report.csv (色调分布)")
            print("- saturation_distribution_report.csv (饱和度分布)")
            print("- brightness_distribution_report.csv (亮度分布)")
            print("- color_temperature_distribution_report.csv (色温分布)")
            print("- detailed_color_analysis.csv (详细色彩数据)")
            print("- color_analysis_summary.txt (汇总报告)")
        else:
            print("\n色彩分析失败，请检查：")
            print("1. 数据文件 data/raw.csv 是否存在")
            print("2. 图像文件路径是否正确")
            print("3. 是否安装了必要的依赖包")
    
    except ImportError as e:
        print(f"\n缺少必要的依赖包: {e}")
        print("请安装以下依赖包：")
        print("pip install opencv-python scikit-learn")
    except Exception as e:
        print(f"\n分析过程中出现错误: {e}")


if __name__ == "__main__":
    main()
