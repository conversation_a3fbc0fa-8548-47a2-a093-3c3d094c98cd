#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件夹选择器测试脚本
测试跨平台文件夹选择功能

Author: ImageAPP
Version: 1.0.0
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.folder_selector import FolderSelector, select_folder

def test_folder_selector():
    """测试文件夹选择器"""
    print("=" * 60)
    print("🧪 文件夹选择器测试")
    print("=" * 60)
    
    print("1. 测试便捷函数...")
    selected = select_folder("请选择一个测试文件夹")
    
    if selected:
        print(f"✅ 选中的文件夹: {selected}")
        print(f"   文件夹存在: {os.path.exists(selected)}")
        print(f"   是否为目录: {os.path.isdir(selected)}")
    else:
        print("❌ 未选择文件夹或操作被取消")
    
    print("\n2. 测试类实例...")
    selector = FolderSelector()
    print(f"   检测到的系统: {selector.system}")
    
    selected2 = selector.select_folder("再次选择一个文件夹")
    
    if selected2:
        print(f"✅ 第二次选中的文件夹: {selected2}")
    else:
        print("❌ 第二次未选择文件夹")
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    test_folder_selector()
