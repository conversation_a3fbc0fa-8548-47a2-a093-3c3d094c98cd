#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台文件夹选择器工具
支持Windows、macOS和Linux系统

Author: ImageAPP
Version: 1.0.0
"""

import os
import sys
import platform
import subprocess


class FolderSelector:
    """跨平台文件夹选择器"""
    
    def __init__(self):
        self.system = platform.system().lower()
    
    def select_folder(self, title="选择文件夹"):
        """选择文件夹，返回选中的路径或None"""
        try:
            if self.system == "darwin":  # macOS
                return self._select_folder_macos(title)
            elif self.system == "windows":  # Windows
                return self._select_folder_windows(title)
            elif self.system == "linux":  # Linux
                return self._select_folder_linux(title)
            else:
                # 回退到tkinter
                return self._select_folder_tkinter(title)
        except Exception as e:
            print(f"文件夹选择器错误: {e}")
            # 如果系统特定的方法失败，尝试tkinter
            return self._select_folder_tkinter(title)
    
    def _select_folder_macos(self, title):
        """macOS系统使用AppleScript"""
        script = f'''
        tell application "System Events"
            activate
            set folderPath to choose folder with prompt "{title}"
            return POSIX path of folderPath
        end tell
        '''
        
        try:
            result = subprocess.run(
                ['osascript', '-e', script],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                path = result.stdout.strip()
                # 移除末尾的斜杠
                if path.endswith('/'):
                    path = path[:-1]
                return path
            else:
                return None
        except subprocess.TimeoutExpired:
            return None
        except Exception:
            return None
    
    def _select_folder_windows(self, title):
        """Windows系统使用PowerShell"""
        script = f'''
        Add-Type -AssemblyName System.Windows.Forms
        $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
        $folderBrowser.Description = "{title}"
        $folderBrowser.ShowNewFolderButton = $true
        $result = $folderBrowser.ShowDialog()
        if ($result -eq [System.Windows.Forms.DialogResult]::OK) {{
            Write-Output $folderBrowser.SelectedPath
        }}
        '''
        
        try:
            result = subprocess.run(
                ['powershell', '-Command', script],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0 and result.stdout.strip():
                return result.stdout.strip()
            else:
                return None
        except subprocess.TimeoutExpired:
            return None
        except Exception:
            return None
    
    def _select_folder_linux(self, title):
        """Linux系统尝试多种方法"""
        # 尝试zenity
        try:
            result = subprocess.run(
                ['zenity', '--file-selection', '--directory', f'--title={title}'],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        # 尝试kdialog
        try:
            result = subprocess.run(
                ['kdialog', '--getexistingdirectory', '.', f'--title={title}'],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        # 回退到tkinter
        return self._select_folder_tkinter(title)
    
    def _select_folder_tkinter(self, title):
        """使用tkinter作为回退方案"""
        try:
            import tkinter as tk
            from tkinter import filedialog
            
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            root.attributes('-topmost', True)  # 置顶显示
            
            # 打开文件夹选择对话框
            folder_path = filedialog.askdirectory(
                title=title,
                mustexist=True
            )
            
            root.destroy()  # 销毁窗口
            return folder_path if folder_path else None
            
        except ImportError:
            print("tkinter不可用，无法打开文件夹选择器")
            return None
        except Exception as e:
            print(f"tkinter文件夹选择器错误: {e}")
            return None


def select_folder(title="选择文件夹"):
    """便捷函数：选择文件夹"""
    selector = FolderSelector()
    return selector.select_folder(title)


if __name__ == "__main__":
    # 测试文件夹选择器
    print("测试文件夹选择器...")
    selected = select_folder("请选择一个文件夹")
    
    if selected:
        print(f"选中的文件夹: {selected}")
    else:
        print("未选择文件夹或取消操作")
