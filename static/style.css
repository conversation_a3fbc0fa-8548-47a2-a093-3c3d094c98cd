/* ImageAPP Web应用自定义样式 */

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
}

.loading-spinner-large {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* 图表容器优化 */
.chart-container canvas {
    max-height: 400px !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }
    
    .stat-number {
        font-size: 2rem !important;
    }
    
    .nav-tabs .nav-link {
        font-size: 0.9rem;
        padding: 8px 12px;
    }
}

/* 路径项样式优化 */
.path-item {
    transition: all 0.3s ease;
}

.path-item:hover {
    background: #e9ecef !important;
    transform: translateX(5px);
}

/* 按钮动画 */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 卡片阴影动画 */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* 进度条样式 */
.progress-bar {
    transition: width 0.3s ease;
}

/* 标签页内容动画 */
.tab-pane {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统计卡片动画 */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: scale(1.05);
}

/* 描述框样式 */
.description-box {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 导航栏样式 */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
}

/* 错误状态样式 */
.error-message {
    color: #dc3545;
    font-size: 0.9rem;
    margin-top: 5px;
}

.success-message {
    color: #28a745;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 工具提示样式 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.tooltip-custom:hover::after {
    opacity: 1;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* 加载状态 */
.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    color: #6c757d;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
