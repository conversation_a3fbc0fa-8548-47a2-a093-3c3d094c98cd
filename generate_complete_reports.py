#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整报告生成脚本（包含色彩分析）
生成所有分析报告，包括基础分析、技术参数分析和色彩风格分析
"""

from modules.report_generator import ReportGenerator


def main():
    """主函数"""
    print("完整照片分析报告生成工具（包含色彩分析）")
    print("=" * 60)
    print("注意：色彩分析功能需要安装额外的依赖包：")
    print("pip install opencv-python scikit-learn")
    print("=" * 60)
    
    # 询问用户是否包含色彩分析
    while True:
        choice = input("是否包含色彩分析？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            include_color = True
            break
        elif choice in ['n', 'no', '否']:
            include_color = False
            break
        else:
            print("请输入 y 或 n")
    
    try:
        # 创建报告生成器实例
        generator = ReportGenerator(include_color_analysis=include_color)
        
        # 生成所有报告
        success = generator.generate_all_reports()
        
        if success:
            print("\n完整报告生成完成！")
            print("请查看 data/reports/ 目录下的所有报告文件。")
            print("\n生成的报告包括：")
            print("- 基础分析报告（设备、时间、镜头等）")
            print("- 技术参数分析报告（焦距、光圈、快门、ISO）")
            print("- 汇总报告（TXT格式）")
            print("- 技术参数综合报告（CSV格式）")
            
            if include_color:
                print("- 色彩分析报告（色调、饱和度、亮度、色温）")
        else:
            print("\n报告生成失败，请检查：")
            print("1. 数据文件 data/raw.csv 是否存在")
            print("2. 图像文件路径是否正确")
            if include_color:
                print("3. 是否安装了色彩分析所需的依赖包")
    
    except Exception as e:
        print(f"\n报告生成过程中出现错误: {e}")
        if include_color:
            print("如果是色彩分析相关错误，请检查是否安装了必要的依赖包：")
            print("pip install opencv-python scikit-learn")


if __name__ == "__main__":
    main()
