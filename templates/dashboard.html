{% extends "base.html" %}

{% block title %}仪表板 - ImageAPP{% endblock %}

{% block content %}
{% if not has_data %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="alert alert-warning text-center">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>暂无数据</strong><br>
            请先在设置页面添加照片路径并开始分析。
            <div class="mt-3">
                <a href="/setup" class="btn btn-primary">
                    <i class="fas fa-cog me-2"></i>
                    前往设置
                </a>
            </div>
        </div>
    </div>
</div>
{% else %}

<!-- 导航标签 -->
<ul class="nav nav-tabs" id="analysisTab" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" 
                data-bs-target="#overview" type="button" role="tab">
            <i class="fas fa-chart-pie me-2"></i>总览
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="parameters-tab" data-bs-toggle="tab" 
                data-bs-target="#parameters" type="button" role="tab">
            <i class="fas fa-chart-bar me-2"></i>参数风格
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="equipment-tab" data-bs-toggle="tab" 
                data-bs-target="#equipment" type="button" role="tab">
            <i class="fas fa-camera me-2"></i>器材洞察
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="time-tab" data-bs-toggle="tab" 
                data-bs-target="#time" type="button" role="tab">
            <i class="fas fa-clock me-2"></i>时间分析
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="color-tab" data-bs-toggle="tab" 
                data-bs-target="#color" type="button" role="tab">
            <i class="fas fa-palette me-2"></i>色彩分析
        </button>
    </li>
</ul>

<!-- 标签内容 -->
<div class="tab-content" id="analysisTabContent">
    <!-- 总览标签 -->
    <div class="tab-pane fade show active" id="overview" role="tabpanel">
        <div class="row">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number" id="totalPhotos">-</div>
                    <div class="stat-label">总照片数量</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number" id="favoriteDevice">-</div>
                    <div class="stat-label">最爱设备</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number" id="favoriteLens">-</div>
                    <div class="stat-label">最爱镜头</div>
                </div>
            </div>
        </div>
        
        <div class="description-box">
            <h6><i class="fas fa-lightbulb me-2"></i>总览洞察</h6>
            <p id="overviewInsight">正在分析您的摄影习惯...</p>
        </div>
    </div>
    
    <!-- 参数风格标签 -->
    <div class="tab-pane fade" id="parameters" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <h5><i class="fas fa-adjust me-2"></i>光圈分布</h5>
                <div class="chart-container">
                    <canvas id="apertureChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <h5><i class="fas fa-stopwatch me-2"></i>快门速度分布</h5>
                <div class="chart-container">
                    <canvas id="shutterChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h5><i class="fas fa-film me-2"></i>ISO分布</h5>
                <div class="chart-container">
                    <canvas id="isoChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <h5><i class="fas fa-search me-2"></i>焦距分布</h5>
                <div class="chart-container">
                    <canvas id="focalChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="description-box">
            <h6><i class="fas fa-camera-retro me-2"></i>参数风格分析</h6>
            <p id="parametersInsight">正在分析您的拍摄参数偏好...</p>
        </div>
    </div>
    
    <!-- 器材洞察标签 -->
    <div class="tab-pane fade" id="equipment" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <h5><i class="fas fa-camera me-2"></i>设备使用分布</h5>
                <div class="chart-container">
                    <canvas id="deviceChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <h5><i class="fas fa-eye me-2"></i>镜头使用分布</h5>
                <div class="chart-container">
                    <canvas id="lensChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="description-box">
            <h6><i class="fas fa-tools me-2"></i>器材使用洞察</h6>
            <p id="equipmentInsight">正在分析您的器材使用习惯...</p>
        </div>
    </div>
    
    <!-- 时间分析标签 -->
    <div class="tab-pane fade" id="time" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <h5><i class="fas fa-sun me-2"></i>时间段分布</h5>
                <div class="chart-container">
                    <canvas id="timePeriodsChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <h5><i class="fas fa-leaf me-2"></i>季节分布</h5>
                <div class="chart-container">
                    <canvas id="seasonChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <h5><i class="fas fa-calendar me-2"></i>月度趋势</h5>
                <div class="chart-container">
                    <canvas id="monthlyChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="description-box">
            <h6><i class="fas fa-clock me-2"></i>时间模式分析</h6>
            <p id="timeInsight">正在分析您的拍摄时间模式...</p>
        </div>
    </div>
    
    <!-- 色彩分析标签 -->
    <div class="tab-pane fade" id="color" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <h5><i class="fas fa-palette me-2"></i>色调分布</h5>
                <div class="chart-container">
                    <canvas id="hueChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <h5><i class="fas fa-tint me-2"></i>饱和度分布</h5>
                <div class="chart-container">
                    <canvas id="saturationChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h5><i class="fas fa-lightbulb me-2"></i>亮度分布</h5>
                <div class="chart-container">
                    <canvas id="brightnessChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <h5><i class="fas fa-thermometer-half me-2"></i>色温分布</h5>
                <div class="chart-container">
                    <canvas id="temperatureChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="description-box">
            <h6><i class="fas fa-rainbow me-2"></i>色彩风格分析</h6>
            <p id="colorInsight">正在分析您的色彩偏好...</p>
        </div>
    </div>
</div>

{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', function() {
    {% if has_data %}
    loadAnalysisData();
    {% endif %}
});

// 加载分析数据
async function loadAnalysisData() {
    try {
        const response = await fetch('/api/analysis');
        const data = await response.json();
        
        if (data.success) {
            updateOverview(data.overview);
            updateParametersCharts(data.parameters);
            updateEquipmentCharts(data.equipment);
            updateTimeCharts(data.time);
            updateColorCharts(data.color);
        }
    } catch (error) {
        console.error('加载数据失败：', error);
    }
}

// 更新总览数据
function updateOverview(data) {
    document.getElementById('totalPhotos').textContent = data.total_photos || 0;
    document.getElementById('favoriteDevice').textContent = data.favorite_device || '未知';
    document.getElementById('favoriteLens').textContent = data.favorite_lens || '未知';
    document.getElementById('overviewInsight').textContent = data.insight || '暂无洞察';
}

// 图表配置
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom'
        }
    }
};

// 更新参数图表
function updateParametersCharts(data) {
    // 光圈图表
    if (data.aperture) {
        new Chart(document.getElementById('apertureChart'), {
            type: 'bar',
            data: data.aperture,
            options: chartOptions
        });
    }

    // 快门图表
    if (data.shutter) {
        new Chart(document.getElementById('shutterChart'), {
            type: 'bar',
            data: data.shutter,
            options: chartOptions
        });
    }

    // ISO图表
    if (data.iso) {
        new Chart(document.getElementById('isoChart'), {
            type: 'bar',
            data: data.iso,
            options: chartOptions
        });
    }

    // 焦距图表
    if (data.focal) {
        new Chart(document.getElementById('focalChart'), {
            type: 'bar',
            data: data.focal,
            options: chartOptions
        });
    }

    document.getElementById('parametersInsight').textContent = data.insight || '暂无分析';
}

// 更新器材图表
function updateEquipmentCharts(data) {
    // 设备图表
    if (data.devices) {
        new Chart(document.getElementById('deviceChart'), {
            type: 'pie',
            data: data.devices,
            options: chartOptions
        });
    }

    // 镜头图表
    if (data.lenses) {
        new Chart(document.getElementById('lensChart'), {
            type: 'pie',
            data: data.lenses,
            options: chartOptions
        });
    }

    document.getElementById('equipmentInsight').textContent = data.insight || '暂无分析';
}

// 更新时间图表
function updateTimeCharts(data) {
    // 时间段图表
    if (data.time_periods) {
        new Chart(document.getElementById('timePeriodsChart'), {
            type: 'line',
            data: data.time_periods,
            options: chartOptions
        });
    }

    // 季节图表
    if (data.seasons) {
        new Chart(document.getElementById('seasonChart'), {
            type: 'pie',
            data: data.seasons,
            options: chartOptions
        });
    }

    // 月度图表
    if (data.monthly) {
        new Chart(document.getElementById('monthlyChart'), {
            type: 'line',
            data: data.monthly,
            options: chartOptions
        });
    }

    document.getElementById('timeInsight').textContent = data.insight || '暂无分析';
}

// 更新色彩图表
function updateColorCharts(data) {
    // 色调图表
    if (data.hue) {
        new Chart(document.getElementById('hueChart'), {
            type: 'pie',
            data: data.hue,
            options: chartOptions
        });
    }

    // 饱和度图表
    if (data.saturation) {
        new Chart(document.getElementById('saturationChart'), {
            type: 'pie',
            data: data.saturation,
            options: chartOptions
        });
    }

    // 亮度图表
    if (data.brightness) {
        new Chart(document.getElementById('brightnessChart'), {
            type: 'pie',
            data: data.brightness,
            options: chartOptions
        });
    }

    // 色温图表
    if (data.temperature) {
        new Chart(document.getElementById('temperatureChart'), {
            type: 'pie',
            data: data.temperature,
            options: chartOptions
        });
    }

    document.getElementById('colorInsight').textContent = data.insight || '暂无分析';
}
</script>
{% endblock %}
