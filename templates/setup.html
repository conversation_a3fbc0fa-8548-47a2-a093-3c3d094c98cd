{% extends "base.html" %}

{% block title %}设置 - ImageAPP{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">
                    <i class="fas fa-folder-open me-2"></i>
                    照片路径设置
                </h2>
                
                {% if not current_paths %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    欢迎使用 ImageAPP！请先选择包含照片的文件夹路径。
                </div>
                {% endif %}
                
                <!-- 添加路径表单 -->
                <div class="mb-4">
                    <h5><i class="fas fa-plus me-2"></i>添加新路径</h5>
                    <form id="addPathForm">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="newPath" 
                                   placeholder="请输入照片文件夹的完整路径，例如：/Users/<USER>/Pictures">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-plus me-1"></i>
                                添加路径
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-muted small">
                        <i class="fas fa-lightbulb me-1"></i>
                        提示：程序会自动扫描该路径下的所有子文件夹中的照片文件
                    </div>
                </div>
                
                <!-- 当前路径列表 -->
                {% if current_paths %}
                <div class="mb-4">
                    <h5><i class="fas fa-list me-2"></i>当前路径</h5>
                    <div id="pathsList">
                        {% for path in current_paths %}
                        <div class="path-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-folder me-2"></i>
                                <span>{{ path }}</span>
                            </div>
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="removePath({{ loop.index0 }})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- 操作按钮 -->
                <div class="text-center">
                    {% if current_paths %}
                    <button class="btn btn-success btn-lg me-3" onclick="startProcessing()">
                        <i class="fas fa-play me-2"></i>
                        开始分析照片
                    </button>
                    {% endif %}
                    
                    <a href="/" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        返回主页
                    </a>
                </div>
                
                <!-- 处理进度 -->
                <div id="processingStatus" class="mt-4" style="display: none;">
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <div class="loading-spinner me-3"></div>
                            <div class="flex-grow-1">
                                <strong>正在处理照片...</strong>
                                <div class="progress mt-2">
                                    <div id="progressBar" class="progress-bar" role="progressbar" 
                                         style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                        0%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 添加路径
document.getElementById('addPathForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const pathInput = document.getElementById('newPath');
    const path = pathInput.value.trim();
    
    if (!path) {
        alert('请输入路径');
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('paths', path);
        
        const response = await fetch('/api/paths', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('路径添加成功！');
            location.reload();
        } else {
            alert('添加失败：' + result.message);
        }
    } catch (error) {
        alert('添加路径时出错：' + error.message);
    }
});

// 删除路径
async function removePath(index) {
    if (!confirm('确定要删除这个路径吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/paths/${index}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('路径删除成功！');
            location.reload();
        } else {
            alert('删除失败：' + result.message);
        }
    } catch (error) {
        alert('删除路径时出错：' + error.message);
    }
}

// 开始处理
async function startProcessing() {
    if (!confirm('开始分析照片？这可能需要一些时间。')) {
        return;
    }
    
    try {
        const response = await fetch('/api/process', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            document.getElementById('processingStatus').style.display = 'block';
            monitorProgress();
        } else {
            alert('启动失败：' + result.message);
        }
    } catch (error) {
        alert('启动处理时出错：' + error.message);
    }
}

// 监控进度
function monitorProgress() {
    const interval = setInterval(async () => {
        try {
            const response = await fetch('/api/progress');
            const progress = await response.json();
            
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = progress.progress + '%';
            progressBar.textContent = progress.progress + '%';
            progressBar.setAttribute('aria-valuenow', progress.progress);
            
            if (!progress.is_processing) {
                clearInterval(interval);
                alert('照片处理完成！');
                location.href = '/';
            }
        } catch (error) {
            console.error('获取进度失败：', error);
        }
    }, 1000);
}
</script>
{% endblock %}
