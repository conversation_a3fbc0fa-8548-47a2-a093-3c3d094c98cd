#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件夹选择器演示脚本
展示跨平台文件夹选择功能

Author: ImageAPP
Version: 1.0.0
"""

import os
import sys
import platform

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_folder_selector():
    """演示文件夹选择器功能"""
    print("=" * 60)
    print("📁 ImageAPP 文件夹选择器演示")
    print("=" * 60)
    
    print(f"🖥️  当前系统: {platform.system()} {platform.release()}")
    print(f"🐍 Python版本: {platform.python_version()}")
    print()
    
    try:
        from utils.folder_selector import select_folder
        
        print("🚀 启动文件夹选择器...")
        print("   请在弹出的对话框中选择一个包含照片的文件夹")
        print()
        
        # 选择文件夹
        selected_folder = select_folder("请选择包含照片的文件夹")
        
        if selected_folder:
            print("✅ 文件夹选择成功！")
            print(f"   选中路径: {selected_folder}")
            print(f"   路径存在: {os.path.exists(selected_folder)}")
            print(f"   是否为目录: {os.path.isdir(selected_folder)}")
            
            # 检查文件夹内容
            if os.path.isdir(selected_folder):
                try:
                    files = os.listdir(selected_folder)
                    print(f"   文件夹内容: {len(files)} 个项目")
                    
                    # 查找图片文件
                    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
                    image_files = [f for f in files if os.path.splitext(f.lower())[1] in image_extensions]
                    
                    if image_files:
                        print(f"   📸 发现 {len(image_files)} 个图片文件")
                        print("   示例文件:")
                        for img in image_files[:5]:  # 显示前5个
                            print(f"     - {img}")
                        if len(image_files) > 5:
                            print(f"     ... 还有 {len(image_files) - 5} 个文件")
                    else:
                        print("   ⚠️  未发现图片文件")
                        
                except PermissionError:
                    print("   ❌ 无权限访问文件夹内容")
                except Exception as e:
                    print(f"   ❌ 读取文件夹内容时出错: {e}")
            
            print("\n🎉 演示完成！这个路径可以用于ImageAPP Web应用。")
            
        else:
            print("❌ 未选择文件夹或操作被取消")
            print("   可能的原因:")
            print("   - 用户取消了选择")
            print("   - 系统不支持图形界面")
            print("   - 缺少必要的依赖")
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("   请确保utils/folder_selector.py文件存在")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
    
    print("\n" + "=" * 60)
    print("💡 提示: 在ImageAPP Web应用中，您可以通过点击")
    print("   '选择照片文件夹' 按钮来使用此功能")

if __name__ == "__main__":
    demo_folder_selector()
