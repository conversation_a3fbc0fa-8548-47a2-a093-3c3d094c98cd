#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ImageAPP Web应用演示脚本
快速演示Web应用功能

Author: ImageAPP
Version: 1.0.0
"""

import os
import json
from modules import BatchProcessor

def create_demo_data():
    """创建演示数据"""
    print("🎬 创建演示数据...")
    
    # 检查是否有Images目录
    if not os.path.exists("Images") or len(os.listdir("Images")) == 0:
        print("❌ 未找到Images目录或目录为空")
        print("请确保Images目录中有照片文件用于演示")
        return False
    
    # 创建演示配置
    demo_config = {
        "photo_paths": ["./Images"],
        "last_update": "2025-07-07T12:00:00"
    }
    
    with open("config.json", "w", encoding="utf-8") as f:
        json.dump(demo_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 演示配置创建完成")
    
    # 处理演示照片
    print("📸 处理演示照片...")
    processor = BatchProcessor()
    results = processor.process_directory("Images", "data/raw.csv")
    
    if results:
        print(f"✅ 成功处理 {len(results)} 张照片")
        return True
    else:
        print("❌ 照片处理失败")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 ImageAPP Web应用演示")
    print("=" * 60)
    
    # 创建必要目录
    os.makedirs("data", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    os.makedirs("templates", exist_ok=True)
    
    # 创建演示数据
    if create_demo_data():
        print("\n🚀 演示数据准备完成！")
        print("现在可以运行以下命令启动Web应用：")
        print("python run_webapp.py")
        print("\n或者直接运行：")
        print("python app.py")
        print("\n然后在浏览器中访问：http://127.0.0.1:8000")
    else:
        print("\n❌ 演示数据准备失败")
        print("请确保Images目录中有照片文件")

if __name__ == "__main__":
    main()
