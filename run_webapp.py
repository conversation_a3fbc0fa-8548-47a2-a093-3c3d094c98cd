#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ImageAPP Web应用启动脚本
快速启动照片分析Web界面

Author: ImageAPP
Version: 1.0.0
"""

import os
import sys
import subprocess
import webbrowser
from time import sleep

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'jinja2',
        'python-multipart',
        'aiofiles',
        'Pillow',
        'pandas',
        'piexif'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖"""
    print("正在安装缺失的依赖包...")
    for package in packages:
        print(f"安装 {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        except subprocess.CalledProcessError:
            print(f"❌ 安装 {package} 失败")
            return False
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 ImageAPP Web应用启动器")
    print("=" * 60)
    
    # 检查依赖
    print("📦 检查依赖包...")
    missing = check_dependencies()
    
    if missing:
        print(f"❌ 缺少以下依赖包: {', '.join(missing)}")
        install_choice = input("是否自动安装缺失的依赖？(y/n): ").lower().strip()
        
        if install_choice == 'y':
            if not install_dependencies(missing):
                print("❌ 依赖安装失败，请手动安装")
                return
        else:
            print("请手动安装依赖包:")
            print(f"pip install {' '.join(missing)}")
            return
    else:
        print("✅ 所有依赖包已安装")
    
    # 创建必要目录
    print("📁 创建必要目录...")
    os.makedirs("static", exist_ok=True)
    os.makedirs("templates", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    print("✅ 目录创建完成")
    
    # 启动Web应用
    print("🌐 启动Web应用...")
    print("应用将在 http://127.0.0.1:8000 启动")
    print("按 Ctrl+C 停止应用")
    print("=" * 60)
    
    # 延迟打开浏览器
    def open_browser():
        sleep(2)
        webbrowser.open("http://127.0.0.1:8000")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动FastAPI应用
    try:
        import uvicorn
        uvicorn.run(
            "app:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
