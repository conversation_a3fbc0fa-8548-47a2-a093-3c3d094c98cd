#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ImageAPP Web Application - FastAPI主应用
照片分析和可视化Web界面

Author: ImageAPP
Version: 1.0.0
"""

import os
import json
from typing import List, Dict, Optional
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, Form, BackgroundTasks
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

from modules import BatchProcessor, PhotoAnalyzer, DuplicateDetector
from utils.folder_selector import select_folder

# 全局配置
CONFIG_FILE = "config.json"
DATA_FILE = "data/raw.csv"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    os.makedirs("static", exist_ok=True)
    os.makedirs("templates", exist_ok=True)
    os.makedirs("data", exist_ok=True)

    # 加载配置
    state.load_config()
    print("ImageAPP Web应用启动成功!")

    yield

    # 关闭时执行（如果需要）
    pass

# 创建FastAPI应用
app = FastAPI(
    title="ImageAPP - 照片分析工具",
    description="专业的照片EXIF信息提取和数据分析工具",
    version="1.0.0",
    lifespan=lifespan
)

# 静态文件和模板配置
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

class AppState:
    """应用状态管理"""
    def __init__(self):
        self.photo_paths: List[str] = []
        self.is_processing = False
        self.processing_progress = 0
        self.total_photos = 0
        self.analysis_data: Optional[Dict] = None
        self.last_update = None
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.photo_paths = config.get('photo_paths', [])
                    return True
            except Exception as e:
                print(f"加载配置失败: {e}")
        return False
    
    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'photo_paths': self.photo_paths,
                'last_update': datetime.now().isoformat()
            }
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False

# 全局状态
state = AppState()

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    # 检查是否需要初始设置
    if not state.photo_paths:
        return templates.TemplateResponse("setup.html", {"request": request})
    
    # 检查数据是否存在
    has_data = os.path.exists(DATA_FILE) and os.path.getsize(DATA_FILE) > 0
    
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "has_data": has_data,
        "photo_paths": state.photo_paths,
        "is_processing": state.is_processing
    })

@app.get("/setup", response_class=HTMLResponse)
async def setup_page(request: Request):
    """设置页面"""
    return templates.TemplateResponse("setup.html", {
        "request": request,
        "current_paths": state.photo_paths
    })

@app.get("/api/select-folder")
async def select_folder():
    """打开系统文件夹选择对话框"""
    try:
        # 在后台线程中运行文件夹选择对话框
        selected_path = await run_folder_dialog()

        if selected_path:
            return {"success": True, "path": selected_path}
        else:
            return {"success": False, "message": "未选择文件夹"}
    except Exception as e:
        return {"success": False, "message": f"打开文件夹选择器失败: {str(e)}"}

def run_folder_dialog_sync():
    """同步运行文件夹选择对话框"""
    try:
        # 使用跨平台文件夹选择器
        folder_path = select_folder("选择包含照片的文件夹")
        return folder_path
    except Exception as e:
        print(f"文件夹选择对话框错误: {e}")
        return None

async def run_folder_dialog():
    """异步运行文件夹选择对话框"""
    import asyncio
    loop = asyncio.get_event_loop()

    # 在线程池中运行同步的文件夹选择对话框
    selected_path = await loop.run_in_executor(None, run_folder_dialog_sync)
    return selected_path

@app.post("/api/paths")
async def add_paths(paths: List[str] = Form(...)):
    """添加照片路径"""
    valid_paths = []

    for path in paths:
        if os.path.exists(path) and os.path.isdir(path):
            if path not in state.photo_paths:
                valid_paths.append(path)

    if valid_paths:
        state.photo_paths.extend(valid_paths)
        state.save_config()
        return {"success": True, "added_paths": valid_paths}

    return {"success": False, "message": "没有找到有效的新路径"}

@app.post("/api/add-selected-path")
async def add_selected_path(path: str = Form(...)):
    """添加选中的路径"""
    if os.path.exists(path) and os.path.isdir(path):
        if path not in state.photo_paths:
            state.photo_paths.append(path)
            state.save_config()
            return {"success": True, "added_path": path}
        else:
            return {"success": False, "message": "路径已存在"}
    else:
        return {"success": False, "message": "无效的路径"}

@app.delete("/api/paths/{path_index}")
async def remove_path(path_index: int):
    """删除照片路径"""
    if 0 <= path_index < len(state.photo_paths):
        removed_path = state.photo_paths.pop(path_index)
        state.save_config()
        return {"success": True, "removed_path": removed_path}
    
    return {"success": False, "message": "无效的路径索引"}

@app.post("/api/process")
async def start_processing(background_tasks: BackgroundTasks):
    """开始处理照片"""
    if state.is_processing:
        return {"success": False, "message": "正在处理中，请稍候"}
    
    if not state.photo_paths:
        return {"success": False, "message": "请先添加照片路径"}
    
    # 启动后台处理任务
    background_tasks.add_task(process_photos_background)
    
    return {"success": True, "message": "开始处理照片"}

@app.get("/api/progress")
async def get_progress():
    """获取处理进度"""
    return {
        "is_processing": state.is_processing,
        "progress": state.processing_progress,
        "total_photos": state.total_photos
    }

@app.get("/api/analysis")
async def get_analysis_data():
    """获取分析数据"""
    if not os.path.exists(DATA_FILE):
        return {"success": False, "message": "数据文件不存在"}

    try:
        # 初始化分析器
        analyzer = PhotoAnalyzer()
        if not analyzer.load_data():
            return {"success": False, "message": "加载数据失败"}

        # 生成分析数据
        analysis_data = {
            "success": True,
            "overview": generate_overview_data(analyzer),
            "parameters": generate_parameters_data(analyzer),
            "equipment": generate_equipment_data(analyzer),
            "time": generate_time_data(analyzer),
            "color": generate_color_data(analyzer)
        }

        return analysis_data

    except Exception as e:
        return {"success": False, "message": f"分析数据时出错: {str(e)}"}

def generate_overview_data(analyzer):
    """生成总览数据"""
    try:
        total_photos = len(analyzer.data)

        # 最爱设备
        device_report = analyzer.analyze_device_usage()
        favorite_device = "未知"
        if device_report and len(device_report) > 0:
            favorite_device = device_report[0].get('设备', '未知')

        # 最爱镜头
        lens_report = analyzer.analyze_lens_usage()
        favorite_lens = "未知"
        if lens_report and len(lens_report) > 0:
            favorite_lens = lens_report[0].get('镜头', '未知')

        # 生成洞察
        if total_photos > 0:
            insight = f"您的摄影作品集包含 {total_photos} 张照片。"
            if favorite_device != "未知":
                insight += f" 您最信赖的拍摄设备是 {favorite_device}，"
            if favorite_lens != "未知":
                insight += f"最常使用的镜头是 {favorite_lens}。"

            if total_photos > 100:
                insight += " 丰富的作品数量展现了您对摄影的热爱和坚持。"
            elif total_photos > 50:
                insight += " 您已经积累了不少摄影作品，继续保持创作热情！"
            else:
                insight += " 这是一个很好的开始，期待看到更多精彩作品！"
        else:
            insight = "暂无照片数据，请先添加照片路径并开始分析。"

        return {
            "total_photos": total_photos,
            "favorite_device": favorite_device,
            "favorite_lens": favorite_lens,
            "insight": insight
        }
    except Exception as e:
        print(f"生成总览数据失败: {e}")
        return {"total_photos": 0, "favorite_device": "未知", "favorite_lens": "未知", "insight": "数据分析失败"}

def generate_parameters_data(analyzer):
    """生成参数数据"""
    try:
        # 光圈分布
        aperture_report = analyzer.analyze_aperture_intervals()
        aperture_data = {
            "labels": [item['光圈区间'] for item in aperture_report] if aperture_report else [],
            "datasets": [{
                "label": "照片数量",
                "data": [item['照片数量'] for item in aperture_report] if aperture_report else [],
                "backgroundColor": [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }

        # 快门速度分布
        shutter_report = analyzer.analyze_shutter_speed_intervals()
        shutter_data = {
            "labels": [item['快门区间'] for item in shutter_report] if shutter_report else [],
            "datasets": [{
                "label": "照片数量",
                "data": [item['照片数量'] for item in shutter_report] if shutter_report else [],
                "backgroundColor": [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }

        # ISO分布
        iso_report = analyzer.analyze_iso_intervals()
        iso_data = {
            "labels": [item['ISO区间'] for item in iso_report] if iso_report else [],
            "datasets": [{
                "label": "照片数量",
                "data": [item['照片数量'] for item in iso_report] if iso_report else [],
                "backgroundColor": [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }

        # 焦距分布
        focal_report = analyzer.analyze_focal_length_intervals()
        focal_data = {
            "labels": [item['焦距区间'] for item in focal_report] if focal_report else [],
            "datasets": [{
                "label": "照片数量",
                "data": [item['照片数量'] for item in focal_report] if focal_report else [],
                "backgroundColor": [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }

        # 生成参数洞察
        insights = []

        if aperture_report:
            # 分析光圈偏好
            max_aperture_item = max(aperture_report, key=lambda x: x['照片数量'])
            insights.append(f"您最常使用的光圈范围是 {max_aperture_item['光圈区间']}")

            # 分析光圈风格
            large_aperture_count = sum(item['照片数量'] for item in aperture_report[:3])  # 前3个区间为大光圈
            total_count = sum(item['照片数量'] for item in aperture_report)
            if large_aperture_count / total_count > 0.6:
                insights.append("您偏爱大光圈拍摄，善于营造浅景深效果")
            elif large_aperture_count / total_count < 0.3:
                insights.append("您更多使用小光圈，注重画面的整体清晰度")

        if shutter_report:
            # 分析快门偏好
            max_shutter_item = max(shutter_report, key=lambda x: x['照片数量'])
            insights.append(f"最常用的快门速度范围是 {max_shutter_item['快门区间']}")

        if iso_report:
            # 分析ISO偏好
            low_iso_count = sum(item['照片数量'] for item in iso_report[:2])  # 前2个区间为低ISO
            total_count = sum(item['照片数量'] for item in iso_report)
            if low_iso_count / total_count > 0.7:
                insights.append("您注重画质，多在光线充足时拍摄")
            else:
                insights.append("您善于在各种光线条件下拍摄")

        insight = "。".join(insights) + "。" if insights else "您的拍摄参数显示了独特的风格偏好。"

        return {
            "aperture": aperture_data,
            "shutter": shutter_data,
            "iso": iso_data,
            "focal": focal_data,
            "insight": insight
        }
    except Exception as e:
        print(f"生成参数数据失败: {e}")
        return {"insight": "参数数据分析失败"}

def generate_equipment_data(analyzer):
    """生成器材数据"""
    try:
        # 设备分布
        device_report = analyzer.analyze_device_usage()
        device_data = {
            "labels": [item['设备'] for item in device_report[:8]] if device_report else [],
            "datasets": [{
                "label": "使用次数",
                "data": [item['使用次数'] for item in device_report[:8]] if device_report else [],
                "backgroundColor": [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }

        # 镜头分布
        lens_report = analyzer.analyze_lens_usage()
        lens_data = {
            "labels": [item['镜头'] for item in lens_report[:8]] if lens_report else [],
            "datasets": [{
                "label": "使用次数",
                "data": [item['使用次数'] for item in lens_report[:8]] if lens_report else [],
                "backgroundColor": [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }

        # 生成器材洞察
        insights = []

        if device_report:
            total_devices = len(device_report)
            if total_devices == 1:
                insights.append(f"您专注使用 {device_report[0]['设备']} 进行拍摄，体现了对单一设备的深度掌握")
            elif total_devices <= 3:
                insights.append(f"您主要使用 {total_devices} 台设备拍摄，设备选择相对集中")
            else:
                insights.append(f"您拥有 {total_devices} 台不同设备，器材丰富多样")

        if lens_report:
            total_lenses = len(lens_report)
            if total_lenses <= 2:
                insights.append("您偏爱使用固定的几支镜头，体现了对特定焦段的偏好")
            elif total_lenses <= 5:
                insights.append("您的镜头搭配较为均衡，能够应对多种拍摄场景")
            else:
                insights.append("您拥有丰富的镜头群，展现了对不同焦段的探索精神")

        insight = "。".join(insights) + "。" if insights else "器材使用数据显示了您的设备偏好和拍摄习惯。"

        return {
            "devices": device_data,
            "lenses": lens_data,
            "insight": insight
        }
    except Exception as e:
        print(f"生成器材数据失败: {e}")
        return {"insight": "器材数据分析失败"}

def generate_time_data(analyzer):
    """生成时间数据"""
    try:
        # 时间段分布
        time_periods_report = analyzer.analyze_time_periods()
        time_periods_data = {
            "labels": [item['时间段'] for item in time_periods_report] if time_periods_report else [],
            "datasets": [{
                "label": "照片数量",
                "data": [item['照片数量'] for item in time_periods_report] if time_periods_report else [],
                "borderColor": '#36A2EB',
                "backgroundColor": 'rgba(54, 162, 235, 0.2)',
                "fill": True
            }]
        }

        # 季节分布
        seasonal_report = analyzer.analyze_seasonal_trends()
        season_data = {
            "labels": [item['季节'] for item in seasonal_report] if seasonal_report else [],
            "datasets": [{
                "label": "照片数量",
                "data": [item['照片数量'] for item in seasonal_report] if seasonal_report else [],
                "backgroundColor": [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'
                ]
            }]
        }

        # 月度趋势
        monthly_report = analyzer.analyze_monthly_trends()
        monthly_data = {
            "labels": [f"{item['月份']}月" for item in monthly_report] if monthly_report else [],
            "datasets": [{
                "label": "照片数量",
                "data": [item['照片数量'] for item in monthly_report] if monthly_report else [],
                "borderColor": '#FF6384',
                "backgroundColor": 'rgba(255, 99, 132, 0.2)',
                "fill": True
            }]
        }

        # 生成时间洞察
        insights = []

        if time_periods_report:
            max_period = max(time_periods_report, key=lambda x: x['照片数量'])
            insights.append(f"您最活跃的拍摄时段是{max_period['时间段']}")

            # 分析拍摄习惯
            morning_count = sum(item['照片数量'] for item in time_periods_report if '早晨' in item['时间段'])
            evening_count = sum(item['照片数量'] for item in time_periods_report if '晚上' in item['时间段'])
            total_count = sum(item['照片数量'] for item in time_periods_report)

            if morning_count > evening_count and morning_count / total_count > 0.4:
                insights.append("您是早起的摄影师，善于捕捉晨光之美")
            elif evening_count > morning_count and evening_count / total_count > 0.4:
                insights.append("您偏爱黄昏时光，享受夕阳西下的浪漫")

        if seasonal_report:
            max_season = max(seasonal_report, key=lambda x: x['照片数量'])
            insights.append(f"您在{max_season['季节']}最为活跃")

        if monthly_report:
            # 找出拍摄最多的月份
            max_month = max(monthly_report, key=lambda x: x['照片数量'])
            insights.append(f"{max_month['月份']}月是您的创作高峰期")

        insight = "，".join(insights) + "。" if insights else "时间分析显示了您的拍摄时间偏好和创作节奏。"

        return {
            "time_periods": time_periods_data,
            "seasons": season_data,
            "monthly": monthly_data,
            "insight": insight
        }
    except Exception as e:
        print(f"生成时间数据失败: {e}")
        return {"insight": "时间数据分析失败"}

def generate_color_data(analyzer):
    """生成色彩数据"""
    try:
        # 由于色彩分析需要额外的依赖，这里提供模拟数据
        # 实际实现需要集成ColorAnalyzer
        # analyzer 参数保留用于未来扩展

        hue_data = {
            "labels": ["红色调", "橙色调", "黄色调", "绿色调", "蓝色调", "紫色调"],
            "datasets": [{
                "label": "照片数量",
                "data": [15, 25, 30, 20, 35, 10],
                "backgroundColor": [
                    '#FF6384', '#FF9F40', '#FFCE56', '#4BC0C0', '#36A2EB', '#9966FF'
                ]
            }]
        }

        saturation_data = {
            "labels": ["低饱和度", "中饱和度", "高饱和度"],
            "datasets": [{
                "label": "照片数量",
                "data": [40, 60, 35],
                "backgroundColor": ['#C9CBCF', '#36A2EB', '#FF6384']
            }]
        }

        brightness_data = {
            "labels": ["暗调", "中调", "亮调"],
            "datasets": [{
                "label": "照片数量",
                "data": [30, 70, 35],
                "backgroundColor": ['#36454F', '#808080', '#F5F5F5']
            }]
        }

        temperature_data = {
            "labels": ["冷色调", "中性", "暖色调"],
            "datasets": [{
                "label": "照片数量",
                "data": [45, 50, 40],
                "backgroundColor": ['#4BC0C0', '#FFCE56', '#FF9F40']
            }]
        }

        insight = "色彩分析显示了您的色彩偏好和风格倾向。注意：完整的色彩分析需要安装额外的依赖包。"

        return {
            "hue": hue_data,
            "saturation": saturation_data,
            "brightness": brightness_data,
            "temperature": temperature_data,
            "insight": insight
        }
    except Exception as e:
        print(f"生成色彩数据失败: {e}")
        return {"insight": "色彩数据分析失败"}

async def process_photos_background():
    """后台处理照片任务"""
    state.is_processing = True
    state.processing_progress = 0

    try:
        # 初始化处理器
        processor = BatchProcessor()
        detector = DuplicateDetector()

        all_results = []

        # 处理每个路径
        for i, path in enumerate(state.photo_paths):
            print(f"处理路径 {i+1}/{len(state.photo_paths)}: {path}")

            # 处理目录
            results = processor.process_directory(path, show_progress=False)
            if results:
                all_results.extend(results)

            # 更新进度
            state.processing_progress = int((i + 1) / len(state.photo_paths) * 50)

        # 去重处理
        if all_results:
            print("开始去重处理...")
            detector.photos = all_results
            detector.remove_duplicates(method='content')
            unique_results = detector.get_unique_photos()

            # 保存到CSV
            detector.save_unique_photos(DATA_FILE)
            state.total_photos = len(unique_results)

            print(f"处理完成，共 {state.total_photos} 张照片")

        state.processing_progress = 100
        state.last_update = datetime.now()

    except Exception as e:
        print(f"处理照片时出错: {e}")
    finally:
        state.is_processing = False

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
